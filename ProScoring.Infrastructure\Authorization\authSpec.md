# Authorization Model Specification for Regatta Scoring Application

## 1. Overview
This authorization model governs access control for **organizing authorities, regattas, and races**, supporting:
- **Hierarchical permissions** (Organizing Authorities → Regattas → Races)
- **Role-based and direct assignments**
- **Explicit permission overrides**
- **Configurable inheritance rules**
- **Database-driven authorization logic**
- **Granular entity-type permissions for creation actions**
- **Application-wide permissions using `UNIVERSAL_TARGET = "*"`**

---

## 2. Core Authorization Actions

### Standard Actions
- `ADMIN` – Full system access, including managing users.
- `DELETE` – Remove an entity.
- `EDIT` – Modify entity details.
- `VIEW` – Read entity information.
- `CREATE` – Create new instances of entity types (e.g., `RaceResults`, `Participant`).

### Entity-Type Permissions
Some actions apply **to entity types** rather than specific instances.
- Example: `"User X can CREATE RaceResults under Regatta123"`
- Example: `"User Y can EDIT all RaceResults under Regatta456"`

### Hierarchical Inheritance
- Users may inherit permissions from an **organizing authority** down to regattas and races.
- Overrides allow explicit changes at the **regatta or race level**.

### Application-Wide Rules
- Some permissions apply **globally** across the entire application.
- `"*"` is treated as an authorization target to signify **application-wide access**.
- Example: `"User X can MANAGE_USERS on '*'"` → Grants global user management rights.

---

## 3. Role-Based vs Direct Permission Assignment

### Roles
- Users inherit permissions via predefined **roles** (e.g., `Regatta Manager`, `Scorekeeper`).
- Roles can apply at **the organizing authority** or **individual regatta level**.

### Direct Assignments
- Users can be granted **specific permissions** at **specific regatta/race levels**.
- Direct assignments **override** role-based permissions when necessary.

### Overrides Handling
- Overrides persist **even if role assignments change**.
- They allow explicit **permission grants** or **denials** per entity.

---

## 4. Data Storage Structure

### Database Tables

#### AuthActions
Stores all available authorization actions.

| Column | Type | Description |
|--------|------|------------|
| `ActionName` | `string` | Name of the action (e.g., `EDIT`, `CREATE`). |

#### ActionHierarchy
Defines parent-child relationships between actions.

| Column | Type | Description |
|--------|------|------------|
| `ParentActionName` | `string` | Parent action granting inference. |
| `ChildActionName` | `string` | Child action inferred from parent. |

#### UserAuthActions
Defines assigned actions for users.

| Column | Type | Description |
|--------|------|------------|
| `UserId` | `string` | Associated user. |
| `TargetId` | `string` | ID of the entity (`Regatta123`, `"*"` for global rules). |
| `EntityType` | `string` | Type of entity affected (e.g., `"RaceResult"`). |
| `ActionName` | `string` | Granted action (`CREATE`, `EDIT`). |

#### OverridePermissions
Tracks explicit permission modifications.

| Column | Type | Description |
|--------|------|------------|
| `OverrideId` | `GUID` | Unique entry ID. |
| `UserId` | `string` | Associated user. |
| `TargetId` | `string` | ID of the entity (regatta/race). |
| `ActionName` | `string` | Modified action. |
| `IsGranted` | `bool` | Whether the action is explicitly granted or denied. |

---

## 5. Authorization Mechanism

### Authorization Check Method
```cs
Task<bool> IsAuthorizedAsync(ClaimsPrincipal actor, string targetId, string action);
